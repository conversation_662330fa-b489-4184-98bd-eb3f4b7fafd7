package com.visionnaire.webpublication.business;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import com.visionnaire.PSS.pid;
import com.visionnaire.PSS.client.Catalog;
import com.visionnaire.PSS.client.PList;
import com.visionnaire.PSS.client.Rel;
import com.visionnaire.webpublication.Webp;
import com.visionnaire.webpublication.base.file.PFile;
import com.visionnaire.webpublication.base.util.Xml;
import com.visionnaire.webpublication.business.exception.BusinessException;
import com.visionnaire.webpublication.business.visualcontent.NewsImpl;
import com.visionnaire.webpublication.business.visualcontent.VisualContentImpl;
import com.visionnaire.webpublication.generation.Html;
import com.visionnaire.webpublication.web.engine.NewsSelectVO;
import com.visionnaire.webpublication.web.engine.NewsletterModelComponentVO;

/**
 * Facade para Newsletter
 * 
 */
public class NewsletterFacade {

	/**
	 * Instância Singleton
	 */
	private static NewsletterFacade instance;

	/**
	 * Recupera a instância Singleton
	 * 
	 * @return ComponentFacade
	 */
	public static NewsletterFacade getInstance() {

		if (instance == null)
			instance = new NewsletterFacade();
		return instance;
	}

	/**
	 * Recupera um objeto NewsletterImpl do banco
	 * 
	 * @param id
	 * @return NewsletterImpl
	 */
	public NewsletterImpl getNewsletterImpl(long id) {

		return Catalog.findByPid(new pid(id), NewsletterImpl.class);
	}

	/**
	 * Recupera um objeto NewsletterModelImpl do banco
	 * 
	 * @param id
	 * @return NewsletterModelImpl
	 */
	public NewsletterModelImpl getNewsletterModelImpl(long id) {

		return Catalog.findByPid(new pid(id), NewsletterModelImpl.class);
	}

	/**
	 * Recupera todas as newsletters cadastradas para o componente indicado passando inicio e fim.
	 * 
	 * @param componentPid
	 * @param begin
	 * @param end
	 * @return newsletter
	 */
	public List<NewsletterImpl> listNewsletters(long componentPid, int begin, int end) {
		
		List<NewsletterImpl> listNewsLetter;
		
		//if(Webp.getProperty("webp.dbname").equals("oracle")){
			//String where = ":component.pid=? AND ROWNUM <= "+ end;	
			String where = ":component.pid=? ";
			listNewsLetter = Rel.restoreNWhere(NewsletterImpl.class, where + " order by :sequence desc", new Object[] { new pid(componentPid) });
		//}else{
		
		//	listNewsLetter = Rel.restoreNWhere(NewsletterImpl.class, ":component.pid=? order by :sequence desc", begin, end, new Object[] { new pid(componentPid) });
		//}
		
		return listNewsLetter;
	}
	
	/**
	 * Recupera todas as newsletters cadastradas para o componente indicado.
	 * 
	 * @param componentPid
	 * @return newsletter
	 */
	public List<NewsletterImpl> listNewsletters(long componentPid) {
		
		List<NewsletterImpl> listNewsLetter = Rel.restoreNWhere(NewsletterImpl.class, ":component.pid=?", new Object[] { new pid(componentPid) });
		return listNewsLetter;
	}

	/**
	 * Retorna o número de newsletters cadastradas
	 * 
	 * @param componentPid
	 * @return int
	 */
	public int countNewsletters(long componentPid) {

		return Rel.count(NewsletterImpl.class, ":component.pid=?", new Object[] { new pid(componentPid) });
	}

	/**
	 * Lista as sequencias que podem ser adicionadas para o componente indicado
	 * 
	 * @param componentPid
	 * @return List
	 */
	public List<Integer> listSequences(long componentPid) {

		List<Integer> sequences = new ArrayList<Integer>();
		int maxSequence = nextNewsletterSequence(componentPid);
		sequences.add(new Integer(maxSequence));

		Object[][] o = Rel.restoreNAttrs(NewsletterImpl.class, ":sequence", ":component.pid = ? order by :sequence desc", false, new Object[] { new pid(componentPid) });
		if (o.length > 0) {
			List<Integer> aux = new ArrayList<Integer>(o.length);
			for (int i = 0; i < o.length; i++) {
				aux.add((Integer) o[i][0]);
			}

			for (int i = maxSequence - 1; i > 0; i--) {
				if (!aux.contains(new Integer(i)))
					sequences.add(new Integer(i));
			}
		}

		return sequences;
	}

	/**
	 * Lista os models cadastrados.
	 * 
	 * @param sitePid
	 * @return List
	 */
	public List<NewsletterModelImpl> listNewsletterModels(long sitePid) {

		return Rel.restoreNWhere(NewsletterModelImpl.class, ":site.pid = ?", new Object[] { new pid(sitePid) });
	}

	/**
	 * Gera a newsletter de acordo com o modelo selecionado. Cria os arquivos no diretório de newsletter do site do componente selecionado. Grava em banco o backup da newsletter gerada em um arquivo compactado.
	 * 
	 * @param componentPid
	 * @param newsletterModelPid
	 * @param newsletterSequence
	 * @throws BusinessException
	 */
	public void generateNewsletter(long componentPid, long newsletterModelPid, int newsletterSequence) throws BusinessException {

		/*
		 * salva o registro no banco de dados calcula o sequencial da newsletter gera os arquivos da newsletter baseando-se no modelo selecionado copia as imagens de newsletter de "/internetfiles/templates/fiep/newsletterimages/*" para
		 * "/internetfiles/fiep/newsletter/data_da_newsletter/*" gera o arquivo newsletter nome do arquivo: newsletter.html recupera o xml dos componentes do site de acordo com o modelo de newsletter selecionado executa a geração do arquivo
		 * html de acordo com o template xsl e os dados em xml gera o arquivo zip do diretório compacta a pasta da newsletter gerada grava o conteúdo binário do arquivo no banco de dados fim
		 */

		ComponentImpl componentImpl = ComponentImpl.restore(componentPid);
		NewsletterModelImpl newsletterModelImpl = getNewsletterModelImpl(newsletterModelPid);
		Date currentDate = new Date();
		int fileSequence = nextFileSequence(componentPid, currentDate);

		// verifica a existência do template
		File xsl = new File(componentImpl.getTemplate());
		if (!xsl.exists()) {
			Catalog.rollback(null, null);
			throw new BusinessException("O arquivo de template da newsletter não existe.");
		}

		// salva a newsletter
		NewsletterImpl newsletterImpl = new NewsletterImpl(new Integer(newsletterSequence), currentDate, new Integer(fileSequence), currentDate, false, componentImpl);

		// copia as imagens
		String originDir = xsl.getParent() + File.separator + NewsletterImpl.IMAGES_PATH;
		String destDir = newsletterImpl.getPathName() + NewsletterImpl.IMAGES_PATH;
		PFile.newDirestory(destDir);

		try {
			PFile.transferTo(originDir, destDir);
		} catch (IOException e) {
			Catalog.rollback(null, null);
			throw new BusinessException("Erro ao copiar as imagens da newsletter", e);
		}

		/*String extension = "";
		if(componentImpl.getSite().isRestricted()){
			extension = Webp.getProperty("webp.dynamic.type");
		}else{
			extension = Webp.getProperty("webp.nodynamic.type");
		} */
			
		// gera o arquivo html da newsletter
		//File html = new File(newsletterImpl.getPathName() + "newsletter." + extension);
		File html = new File(newsletterImpl.getPathName() + "newsletter.html");
		String libPath = componentImpl.getSite().getElementStruct().getPath();
		String xml = getNewsletterXML(newsletterImpl, newsletterModelImpl);

		// // init test
		// try{
		// FileOutputStream filetest = new FileOutputStream("c:\\Temp\\newsletter.xml");
		// filetest.write(xml.getBytes());
		// filetest.close();
		// }catch(Exception e) {e.printStackTrace();}
		// // end test

		Html.make(xml, xsl, html, libPath , componentImpl.getSite().isRestricted(), componentImpl.getSite().getEncodingXML());
		// Html.makeNewsletter(xml, xsl, html, libPath);

		// compacta o diretório e grava o arquivo zip em disco e no banco de dados
		String directoryPath = newsletterImpl.getPathName();
		String zipFile = newsletterImpl.getZipFileName();
		byte[] zippedData = zipDirectory(directoryPath, zipFile);
		newsletterImpl.setZippedData(zippedData);

		// commit
		Catalog.commit();
	}

	/**
	 * Exclui a newsletter em banco. Exclui os arquivos gerados.
	 * 
	 * @param newsletterPid
	 * @throws BusinessException
	 */
	public void deleteNewsletter(long newsletterPid) throws BusinessException {

		NewsletterImpl newsletterImpl = getNewsletterImpl(newsletterPid);
		//if (newsletterImpl.isSent())
		//	throw new BusinessException("A newsletter selecionada está marcada como enviada. Não é possível excluir");

		try {
			// remove o arquivo zip
			PFile.delete(newsletterImpl.getZipFileName(), "deleteNewsletter: " + newsletterPid);

			// remove o diretório
			PFile.deleteDirestory(newsletterImpl.getPathName());
		} catch (IOException e) {
			throw new BusinessException("Erro ao deletar os arquivos da newsletter: " + newsletterPid, e);
		}

		// remove o registro
		newsletterImpl.pssDelete();
		Catalog.commit();
	}

	/**
	 * Exclui a newsletter em banco. Exclui os arquivos gerados.
	 * 
	 * @param newsletterPid
	 * @throws BusinessException
	 */
	public void regenerateNewsletter(long newsletterPid) throws BusinessException {

		NewsletterImpl newsletterImpl = getNewsletterImpl(newsletterPid);
		newsletterImpl.setFileCreationDate(new Date());

		try {
			// remove o arquivo zip
			PFile.delete(newsletterImpl.getZipFileName(), "deleteNewsletter: " + newsletterPid);
			// remove o diretório
			PFile.deleteDirestory(newsletterImpl.getPathName());

			// cria o diretório base
			PFile.newDirestory(newsletterImpl.getNewslettersPath());

			// copia o conteúdo compactado do banco de dados para o disco
			byte[] zipData = newsletterImpl.getZippedData();
			String zipFileName = newsletterImpl.getZipFileName();
			FileOutputStream out = new FileOutputStream(zipFileName);
			out.write(zipData);
			out.close();

			// descompacta o zip no próprio diretório (extract here)
			unzipFile(zipFileName);
		} catch (IOException e) {
			Catalog.rollback(null, null);
			throw new BusinessException("Erro ao regerar os arquivos da newsletter: " + newsletterPid, e);
		}

		// salva as alterações
		Catalog.commit();
	}

	/**
	 * Salva/atualiza um objeto NewsletterModel
	 * 
	 * @param name
	 * @param sitePid
	 * @param voList
	 * @throws BusinessException
	 */
	@SuppressWarnings("boxing")
	public void saveNewsletterModel(String name, long sitePid, List<NewsletterModelComponentVO> voList) throws BusinessException {

		SiteImpl siteImpl = SiteImpl.restore(sitePid);
		NewsletterModelImpl newsletterModelImpl = new NewsletterModelImpl(name, new Date(), siteImpl);

		List<NewsletterModelComponentImpl> modelCompList = new ArrayList<>();
		// cria as novas referências
		for (NewsletterModelComponentVO vo : voList) {
			ComponentImpl component = ComponentImpl.restore(vo.getComponentPid());
			NewsletterModelComponentImpl nmc = new NewsletterModelComponentImpl(new Integer(vo.getOrder()), new Integer(vo.getQuantity()), newsletterModelImpl, component);
			newsletterModelImpl.getNewsletterModelComponents().add(nmc);
			
			modelCompList.add(nmc);
		}
		Catalog.commit();
		
		int i = 0;
		for (NewsletterModelComponentVO vo : voList) {
			if(!vo.getNewsSelectVO().isEmpty()){
				for (NewsSelectVO idNews : vo.getNewsSelectVO())
				{
					saveNewsletterModelNews(modelCompList.get(i).getPid().getSerial(), idNews.getNewsSelectId());
				}
			}
			i++;
		}
	}

	/**
	 * Atualiza o modelo de newsletter.
	 * 
	 * @param newsletterModelPid
	 * @param name
	 * @param voList
	 * @throws BusinessException
	 */
	@SuppressWarnings("boxing")
	public void updateNewsletterModel(long newsletterModelPid, String name, List<NewsletterModelComponentVO> voList) throws BusinessException {

		// verifica se existe alguma newsletter utilizando o modelo.
		/*
		 * if (isNewsletterModelUsed(newsletterModelPid)) { throw new BusinessException("O modelo não pode ser alterado pois existem newsletters associadas."); }
		 */

		// inicia as modificações
		Catalog.begin();

		NewsletterModelImpl newsletterModelImpl = getNewsletterModelImpl(newsletterModelPid);
		newsletterModelImpl.setName(name);

		// deleta as referências antigas
		for (NewsletterModelComponentImpl nmc : newsletterModelImpl.getNewsletterModelComponents()) {
			List<NewsletterModelNewsImpl> modelNewsImpls = Rel.restoreNWhere(NewsletterModelNewsImpl.class, ":newsletterModelComp = " + nmc.getPid().getSerial());
			/*for (NewsletterModelNewsImpl newsletterModelNewsImpl : modelNewsImpls){
				Catalog.findByPid(newsletterModelNewsImpl.getPid(), NewsletterModelNewsImpl.class).pssDelete();
			}*/
			for(int i=0; i < modelNewsImpls.size(); i++){
				Catalog.findByPid(modelNewsImpls.get(i).getPid(), NewsletterModelNewsImpl.class).pssDelete();
			}
			nmc.pssDelete();
		}

		Catalog.flush();

		List<NewsletterModelComponentImpl> modelCompList = new ArrayList<>();
		// cria as novas referências
		for (NewsletterModelComponentVO vo : voList) {
			ComponentImpl component = ComponentImpl.restore(vo.getComponentPid());
			NewsletterModelComponentImpl nmc = new NewsletterModelComponentImpl(new Integer(vo.getOrder()), new Integer(vo.getQuantity()), newsletterModelImpl, component);
			newsletterModelImpl.getNewsletterModelComponents().add(nmc);
			
			modelCompList.add(nmc);
		}
		
		Catalog.commit();
		
		int i = 0;
		for (NewsletterModelComponentVO vo : voList) {
			if(!vo.getNewsSelectVO().isEmpty()){
				for (NewsSelectVO idNews : vo.getNewsSelectVO())
				{
					saveNewsletterModelNews(modelCompList.get(i).getPid().getSerial(), idNews.getNewsSelectId());
				}
			}
			i++;
		}
	}

	/**
	 * Exclui o modelo. Apenas se não estiver sendo usado.
	 * 
	 * @param newsletterModelPid
	 * @throws BusinessException
	 */
	public void deleteNewsletterModel(long newsletterModelPid) throws BusinessException {

		// verifica se existe alguma newsletter utilizando o modelo.
		/*
		 * if (isNewsletterModelUsed(newsletterModelPid)) { throw new BusinessException("O modelo não pode ser excluído pois existem newsletters associadas."); }
		 */

		NewsletterModelImpl model = getNewsletterModelImpl(newsletterModelPid);
		// NewsletterImpl nTemp = Catalog.findByPid(model.getPid(), NewsletterImpl.class);

		if (model != null) {
			if (!model.getNewsletterModelComponents().isEmpty()) {
				for (NewsletterModelComponentImpl cT : model.getNewsletterModelComponents()) {
					NewsletterModelComponentImpl modelComp = Catalog.findByPid(cT.getPid(), NewsletterModelComponentImpl.class);
					
					if (modelComp != null) {
						List<NewsletterModelNewsImpl> modelNewsImpls = Rel.restoreNWhere(NewsletterModelNewsImpl.class, ":newsletterModelComp = " + modelComp.getPid().getSerial());
						for(int i=0; i < modelNewsImpls.size(); i++){
							Catalog.findByPid(modelNewsImpls.get(i).getPid(), NewsletterModelNewsImpl.class).pssDelete();
						}
						/*for (NewsletterModelNewsImpl newsletterModelNewsImpl : modelNewsImpls){
							Catalog.findByPid(newsletterModelNewsImpl.getPid(), NewsletterModelNewsImpl.class).pssDelete();
						}*/
						
						modelComp.pssDelete();
					}
				}
			}
			model.pssDelete();
			Catalog.commit();
		}
	}

	/**
	 * Marca a newsletter como enviada
	 * 
	 * @param newsletterPid
	 * @param send
	 */
	public void markAsSent(long newsletterPid, boolean send) {

		NewsletterImpl newsletterImpl = getNewsletterImpl(newsletterPid);
		newsletterImpl.setSent(send);
		Catalog.commit();
	}

	/**
	 * Lista os componentes disponíveis para o modelo de newsletter.
	 * 
	 * @param sitePid
	 * @param voListTemp
	 * @return List<NewsletterModelComponentVO>
	 */
	public List<NewsletterModelComponentVO> listAvailableComponents(long sitePid, List<NewsletterModelComponentVO> voListTemp) {

		List<NewsletterModelComponentVO> vos = new ArrayList<NewsletterModelComponentVO>();
		List<ComponentImpl> components = SiteFacade.restoreSiteComponents(sitePid);
		for (ComponentImpl component : components) {
			NewsletterModelComponentVO vo = new NewsletterModelComponentVO();

			// seta no vo caso a pessoa nao digite o nome na tela de cadastro, dai a lista vem com os valores order e quantity.
			if (voListTemp != null) {
				for (NewsletterModelComponentVO n : voListTemp) {
					if (n.getComponentPid() == component.getPidLong().longValue()) {
						vo.setQuantity(n.getQuantity());
						vo.setOrder(n.getOrder());
						vo.setSelected(true);
					}
				}
			}

			vo.setComponentPid(component.getPid().getSerial());
			vo.setComponentName(component.getName());
			vos.add(vo);
		}

		return vos;
	}

	/**
	 * Lista os componentes disponíveis para o modelo de newsletter mas atribui "selected=true" para os que pertencem ao modelo de newsletter.
	 * 
	 * @param newsletterModelPid
	 * @return List<NewsletterModelComponentVO>
	 */
	public List<NewsletterModelComponentVO> listSelectedComponents(long newsletterModelPid) {

		// componentes selecionados
		NewsletterModelImpl newsletterModel = getNewsletterModelImpl(newsletterModelPid);
		Map<Long, NewsletterModelComponentImpl> selectedComponents = new HashMap<Long, NewsletterModelComponentImpl>();
		for (NewsletterModelComponentImpl nmc : newsletterModel.getNewsletterModelComponents()) {
			selectedComponents.put(nmc.getComponent().getPidLong(), nmc);
		}

		// Componentes disponíveis
		List<NewsletterModelComponentVO> vos = new ArrayList<NewsletterModelComponentVO>();
		List<ComponentImpl> components = SiteFacade.restoreSiteComponents(newsletterModel.getSite().getPid().getSerial());
		for (ComponentImpl component : components) {
			NewsletterModelComponentVO vo = new NewsletterModelComponentVO();
			vo.setComponentPid(component.getPid().getSerial());
			vo.setComponentName(component.getName());

			if (selectedComponents.containsKey(component.getPidLong())) {
				NewsletterModelComponentImpl nmc = selectedComponents.get(component.getPidLong());
				vo.setSelected(true);
				vo.setOrder(nmc.getOrderNumber().intValue());
				vo.setQuantity(nmc.getQuantity().intValue());
				
				if("News".equals(component.getType())){
					vo.setNewsSelectVO(recuperarNewsMocelCompNews(nmc));
				}
			}
			vos.add(vo);
		}

		return vos;
	}
	
	/**
	 * @param nmc
	 * @return NewsSelectVO
	 */
	private List<NewsSelectVO> recuperarNewsMocelCompNews(NewsletterModelComponentImpl nmc){
		List<NewsSelectVO> retorno = new ArrayList<>();
		PList<NewsletterModelNewsImpl> modelNewsImpls = Rel.restoreNWhere(NewsletterModelNewsImpl.class, ":newsletterModelComp = " + nmc.getPid().getSerial());
		for (NewsletterModelNewsImpl newsletterModelNewsImpl : modelNewsImpls)
		{
			NewsImpl news = Catalog.findByPid(newsletterModelNewsImpl.getNews().getPid(), NewsImpl.class);
			retorno.add(new NewsSelectVO(String.valueOf(newsletterModelNewsImpl.getNews().getPid().getSerial()), news.getTitle()) );
		}
		
		return retorno;
	}
	
	/**
	 * Retorna true caso o modelo de newsletter esteja sendo utilizado por alguma newsletter.
	 * 
	 * @param newsletterModelPid
	 * @return boolean
	 */
	public boolean isNewsletterModelUsed(long newsletterModelPid) {

		// verifica se existe alguma newsletter utilizando o modelo.
		int numOfNewsletters = Rel.count(NewsletterImpl.class, ":newsletterModel.pid=?", new Object[] { new pid(newsletterModelPid) });
		return numOfNewsletters != 0;
	}

	/**
	 * recupera o maior valor de sequence das newsletters
	 * 
	 * @param componentPid
	 * @return max sequence
	 */
	private int nextNewsletterSequence(long componentPid) {

		Object[][] o = Rel.restoreNAttrs(NewsletterImpl.class, "MAX(:sequence)", ":component.pid = ?", false, new Object[] { new pid(componentPid) });

		int sequence = 0;
		if (o[0][0] != null)
			sequence = ((Integer) o[0][0]).intValue();

		return ++sequence;
	}

	/**
	 * Compacta o diretório e grava em disco.
	 * 
	 * @param directoryPath
	 * @param zipFile
	 * @return byte[] do arquivo zipado
	 * @throws BusinessException
	 */
	private byte[] zipDirectory(String directoryPath, String zipFile) throws BusinessException {

		try {
			// cria o arquivo zip
			ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile));

			// compacta o diretório e seus sub-diretórios
			zipEntry(out, new File(directoryPath), null);

			// finaliza a geração do arquivo zip
			out.close();

			// lê o arquivo gerado e retorna em array de bytes
			FileInputStream in = new FileInputStream(zipFile);
			ByteArrayOutputStream bytes = new ByteArrayOutputStream();
			byte[] buf = new byte[1024];
			int len;

			while ((len = in.read(buf)) > 0)
				bytes.write(buf, 0, len);

			in.close();

			// retorna o conteúdo do zip
			return bytes.toByteArray();
		} catch (IOException e) {
			throw new BusinessException("Erro ao gerar o arquivo zip da newsletter.", e);
		}
	}

	/**
	 * Compacta o diretório e grava em disco.
	 * 
	 * @param zipFileName
	 * @throws BusinessException
	 */
	private void unzipFile(String zipFileName) throws BusinessException {

		try {
			File zipFile = new File(zipFileName);
			String baseDir = zipFile.getParent();

			ZipInputStream in = new ZipInputStream(new FileInputStream(zipFile));

			byte[] buf = new byte[1024];
			ZipEntry zipEntry = null;
			while ((zipEntry = in.getNextEntry()) != null) {
				File fileEntry = new File(baseDir + File.separator + zipEntry.getName());
				fileEntry.getParentFile().mkdirs();

				FileOutputStream out = new FileOutputStream(fileEntry);
				int len = 0;
				while ((len = in.read(buf)) > 0) {
					out.write(buf, 0, len);
				}
				out.close();
				in.closeEntry();
			}
			in.close();
		} catch (IOException e) {
			throw new BusinessException("Erro descompactar o arquivo zip da newsletter.", e);
		}
	}

	/**
	 * Compacta o arquivo/diretório especificado dentro de um zip
	 * 
	 * @param out
	 * @param file
	 * @param baseDir
	 *            determina
	 * @throws IOException
	 */
	private static void zipEntry(ZipOutputStream out, File file, String baseDir) throws IOException {

		if (baseDir == null)
			baseDir = "";

		// recursive
		if (file.isDirectory()) {
			baseDir += file.getName() + File.separator;
			File[] files = file.listFiles();

			for (int i = 0; i < files.length; i++) {
				zipEntry(out, files[i], baseDir);
			}
			out.flush();
		}
		else {
			// cria uma nova entrada no zip de acordo com o diretório base para este arquivo
			out.putNextEntry(new ZipEntry(baseDir + file.getName()));

			byte[] buf = new byte[1024];
			@SuppressWarnings("resource")
            FileInputStream in = new FileInputStream(file);

			int len;
			while ((len = in.read(buf)) > 0)
				out.write(buf, 0, len);
			out.closeEntry();
		}
	}

	/**
	 * Recupera o xml dos componentes da newsletter
	 * 
	 * @param newsletterImpl
	 * @param newsletterModelImpl
	 * @return String
	 * @throws BusinessException 
	 */
	private String getNewsletterXML(NewsletterImpl newsletterImpl, NewsletterModelImpl newsletterModelImpl) throws BusinessException {

		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

		StringBuilder sb = new StringBuilder();
		sb.append("<newsletter>");
				
		sb.append("<webpUrl>"+Webp.getProperty("webp.url")+"</webpUrl>");

		sb.append(Xml.makeTag("sequence", newsletterImpl.getSequence().intValue()));
		sb.append(Xml.makeTag("pathName", newsletterImpl.getPathName()));

		if (newsletterImpl.getCreationDate() != null) {
			sb.append("<creationDate>");
			sb.append(Xml.makeTagXml("fullDate", sdf.format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("fullYear", new SimpleDateFormat("yyyy").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("year", new SimpleDateFormat("yy").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("monthInYear", new SimpleDateFormat("MM").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("monthInYear_text", new SimpleDateFormat("MMMMMMMM").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("dayInMonth", new SimpleDateFormat("dd").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("dayInWeek", new SimpleDateFormat("EEEEEEEEEEEE").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("hours", new SimpleDateFormat("HH").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("minutes", new SimpleDateFormat("mm").format(newsletterImpl.getCreationDate())));
			sb.append(Xml.makeTagXml("seconds", new SimpleDateFormat("ss").format(newsletterImpl.getCreationDate())));
			sb.append("</creationDate>");
		}
		sb.append(Xml.makeTagXml("fileName", newsletterImpl.getFileName()));
		sb.append(Xml.makeTag("fileSequence", newsletterImpl.getFileSequence().intValue()));
		sb.append(Xml.makeTagXml("fileCreationDate", sdf.format(newsletterImpl.getFileCreationDate())));
		sb.append(Xml.makeTagXml("zipFileName", newsletterImpl.getZipFileName()));
		sb.append(Xml.makeTagXml("modelName", newsletterModelImpl.getName()));
		sb.append(Xml.makeTagXml("modelCreationDate", sdf.format(newsletterModelImpl.getCreationDate())));

		sb.append("<site>");
		sb.append(Xml.makeTag("sitePid", newsletterImpl.getComponent().getSite().getPid().getSerial()));
		sb.append(Xml.makeTagXml("siteName", newsletterImpl.getComponent().getSite().getName()));
		sb.append(Xml.makeTagXml("siteUrl", newsletterImpl.getComponent().getSite().getFullUrl()));
		sb.append(Xml.makeTagXml("siteUrlNoFile", newsletterImpl.getLinkNoFile()));
		sb.append(Xml.makeTagXml("newsletterUrl", newsletterImpl.getLink()));
		sb.append("</site>");

		sb.append("<components>");
		for (NewsletterModelComponentImpl o : newsletterModelImpl.getNewsletterModelComponents()) {
			StringBuilder sbComponent = new StringBuilder();
			sbComponent.append(Xml.makeTag("newsletterOrder", o.getOrderNumber().intValue()));
			sbComponent.append(Xml.makeTag("newsletterQuantity", o.getQuantity().intValue()));

			List<VisualContentImpl> contentNews = new ArrayList<>();
			if("News".equals(o.getComponent().getType())){
				List<NewsletterModelNewsImpl> modelNewsImpls = Rel.restoreNWhere(NewsletterModelNewsImpl.class, ":newsletterModelComp = " + o.getPid().getSerial());
				for (NewsletterModelNewsImpl newsletterModelNewsImpl : modelNewsImpls)
				{
					NewsImpl news = Catalog.findByPid(newsletterModelNewsImpl.getNews().getPid(), NewsImpl.class);
					contentNews.add(news);
				}
			}
			
			sbComponent.append("<contents>");
			
			long typeOrderByParameter = -1;
			
			VisualContentImpl contents[] = null;
			
			if(!contentNews.isEmpty()){
				contents = new VisualContentImpl[contentNews.size()];
				contents = contentNews.toArray(contents);
			}else{
				contents = o.getComponent().getContentsApproveds(false, null, 1, o.getQuantity().intValue(), o.getComponent().getOrdenacao(), false, false, null, null, null, typeOrderByParameter, "", null, null, null, null, null,
								null,null,null,null, 0, 0);
			}
			
			if(Webp.getProperty("webp.client").equals("CITS") && o.getComponent().getType().equals(Constants.EVENT_AGENDA))
			{
				VisualContentImpl contents2[] = o.getComponent().getContentsApproveds(false, null, 1, 0, o.getComponent().getOrdenacao(), false, false, null, null, null, typeOrderByParameter, "", null, null, null, null, null,
					null,null,null,null, 0, 0);
				
				List<VisualContentImpl> listt = Arrays.asList(contents2);
				Collections.sort(listt, new Comparator<VisualContentImpl>() {
					@Override
					public int compare (VisualContentImpl a, VisualContentImpl b)
					{
						return b.getEditionDate().compareTo(a.getEditionDate());
					}
				});
				
				for (int i=0; i < o.getQuantity().intValue(); i++)
					sbComponent.append(listt.get(i).getXml(null, null, true, false, "", 0));
				
			}
			else if(Webp.getProperty("webp.client").equals("CRM-PR") && o.getComponent().getType().equals(Constants.EVENT_AGENDA)){

				VisualContentImpl contents2[] = o.getComponent().getContentsApproveds(false, null, 1, 0, o.getComponent().getOrdenacao(), false, false, null, null, null, typeOrderByParameter, "", null, null, null, null, null,
					null,null,null,null, 0, 0);

				List<VisualContentImpl> listt = Arrays.asList(contents2);
				Date today = new Date();
				
				// Filter future events and sort by date ascending
				listt = listt.stream()
					.filter(content -> content.getEditionDate().after(today) || content.getEditionDate().equals(today))
					.sorted(Comparator.comparing(VisualContentImpl::getEditionDate))
					.collect(Collectors.toList());
				
				for (int i=0; i < Math.min(o.getQuantity().intValue(), listt.size()); i++) {
					sbComponent.append(listt.get(i).getXml(null, null, true, false, "", 0));
				}

				if(listt.size() < o.getQuantity().intValue()) {
					for (int i=0; i < o.getQuantity().intValue() - listt.size(); i++) {
						sbComponent.append(listt.get(i).getXml(null, null, true, false, "", 0));
					}
				}

			}
			else
			{
				for (VisualContentImpl content : contents)
					sbComponent.append(content.getXml(null, null, true, false, "", 0));
			}
			
			sbComponent.append("</contents>");

			sb.append(o.getComponent().getXml(true, sbComponent.toString(), 0, 0, false, 0, null, null, -1, null, null, null, null, null, null));
		}
		sb.append("</components>");
		sb.append("</newsletter>");

		String sTemp = "";
		if(Webp.getProperty("webp.client").equals("CITS") || Webp.getProperty("webp.client").equals("FIEP") || Webp.getProperty("webp.client").equals("VISIONNAIRE") ){
			sTemp = sb.toString();
		}else{
			sTemp = escapeHTML(sb.toString());
		}
			
		
		/*if(newsletterImpl.getComponent().getSite().isRestricted()){
			sTemp = sTemp.concat("<restricted>" + newsletterImpl.getComponent().getSite().getRestrict() +"</restricted>");	
		}*/

		return sTemp;
	}

	/**
	 * @param texto
	 * @return String
	 */
	public String corrigeAcentuacao(String texto) {

		texto = texto.replace("Ã¡", "á").replace("Ã¢", "â").replace("Ã£", "ã").replace("Ã©", "é").replace("Ãª", "ê").replace("Ã­", "í").replace("Ã®", "î").replace("Ã³", "ó").replace("Ã´", "ô").replace("Ãµ ", "õ").replace("Ãº", "ú")
				.replace("Ã»", "û");
		texto = texto.replace("Ã?", "Á").replace("Ã‚", "Â").replace("Ãƒ", "Ã").replace("Ã‰", "É").replace("ÃŠ", "Ê").replace("Ã?", "Í").replace("Ã“", "Ó").replace("Ã”", "Ô").replace("Ã•", "Õ").replace("Ãš", "Ú");
		texto = texto.replace("Ã§", "ç").replace("Ã‡", "Ç").replace("Âº", "º");
		return texto;
	}

	/**
	 * @param s
	 * @return string
	 * 
	 */
	public static final String escapeHTML(String s) {

		StringBuffer sb = new StringBuffer();
		int n = s.length();
		for (int i = 0; i < n; i++) {
			char c = s.charAt(i);
			switch (c) {

			// case '<': sb.append("&lt;"); break;
			// case '>': sb.append("&gt;"); break;
				case '&':
					sb.append("&amp;");
					break;
				// case '"': sb.append("&quot;"); break;
				case 'à':
					sb.append("&agrave;");
					break;
				case 'À':
					sb.append("&Agrave;");
					break;
				case 'á':
					sb.append("&aacute;");
					break;
				case 'Á':
					sb.append("&Aacute;");
					break;
				case 'â':
					sb.append("&acirc;");
					break;
				case 'Â':
					sb.append("&Acirc;");
					break;
				case 'ã':
					sb.append("&atilde;");
					break;
				case 'Ã':
					sb.append("&Atilde;");
					break;
				case 'ä':
					sb.append("&auml;");
					break;
				case 'Ä':
					sb.append("&Auml;");
					break;
				case 'å':
					sb.append("&aring;");
					break;
				case 'Å':
					sb.append("&Aring;");
					break;
				case 'æ':
					sb.append("&aelig;");
					break;
				case 'Æ':
					sb.append("&AElig;");
					break;
				case 'ç':
					sb.append("&ccedil;");
					break;
				case 'Ç':
					sb.append("&Ccedil;");
					break;
				case 'é':
					sb.append("&eacute;");
					break;
				case 'É':
					sb.append("&Eacute;");
					break;
				case 'è':
					sb.append("&egrave;");
					break;
				case 'È':
					sb.append("&Egrave;");
					break;
				case 'ê':
					sb.append("&ecirc;");
					break;
				case 'Ê':
					sb.append("&Ecirc;");
					break;
				case 'ë':
					sb.append("&euml;");
					break;
				case 'Ë':
					sb.append("&Euml;");
					break;
				case 'í':
					sb.append("&iacute;");
					break;
				case 'Í':
					sb.append("&Iacute;");
					break;
				case 'ï':
					sb.append("&iuml;");
					break;
				case 'Ï':
					sb.append("&Iuml;");
					break;
				case 'ó':
					sb.append("&oacute;");
					break;
				case 'Ó':
					sb.append("&Oacute;");
					break;
				case 'ô':
					sb.append("&ocirc;");
					break;
				case 'Ô':
					sb.append("&Ocirc;");
					break;
				case 'õ':
					sb.append("&otilde;");
					break;
				case 'Õ':
					sb.append("&Otilde;");
					break;
				case 'ö':
					sb.append("&ouml;");
					break;
				case 'Ö':
					sb.append("&Ouml;");
					break;
				case 'ø':
					sb.append("&oslash;");
					break;
				case 'Ø':
					sb.append("&Oslash;");
					break;
				case 'ß':
					sb.append("&szlig;");
					break;
				case 'ú':
					sb.append("&uacute;");
					break;
				case 'Ú':
					sb.append("&Uacute;");
					break;
				case 'ù':
					sb.append("&ugrave;");
					break;
				case 'Ù':
					sb.append("&Ugrave;");
					break;
				case 'û':
					sb.append("&ucirc;");
					break;
				case 'Û':
					sb.append("&Ucirc;");
					break;
				case 'ü':
					sb.append("&uuml;");
					break;
				case 'Ü':
					sb.append("&Uuml;");
					break;
				case 'ª':
					sb.append("&ordf;");
					break;
				case 'º':
					sb.append("&ordm;");
					break;
					
				// case '®': sb.append("&reg;");break;
				// case '©': sb.append("&copy;");break;
				// case '€': sb.append("&euro;"); break;
				// be carefull with this one (non-breaking whitee space)
				// case ' ': sb.append("&nbsp;");break;

				default:
					sb.append(c);
					break;
			}
		}
		return sb.toString();
	}

	/**
	 * Recupera o texto do mes passado como número
	 * 
	 * @param number
	 * @return String
	 */
	public String recuperaMesTexto(String number) {

		int u = Integer.parseInt(number);

		switch (u) {
			case 1:
				return "Janeiro";
			case 2:
				return "Fevereiro";
			case 3:
				return "Março";
			case 4:
				return "Abril";
			case 5:
				return "Maio";
			case 6:
				return "Junho";
			case 7:
				return "Julho";
			case 8:
				return "Agosto";
			case 9:
				return "Setembro";
			case 10:
				return "Outubro";
			case 11:
				return "Novembro";
			case 12:
				return "Dezembro";
		}
		return null;
	}

	/**
	 * Recupera o maior valor de sequence dos arquivos gerados das newsletters
	 * 
	 * @param componentPid
	 * @param currentDate
	 * @return max sequence
	 */
	private int nextFileSequence(long componentPid, Date currentDate) {

		Calendar currCalendar = Calendar.getInstance();
		currCalendar.setTime(currentDate);

		Calendar aux = Calendar.getInstance();
		aux.clear();
		aux.set(currCalendar.get(Calendar.YEAR), currCalendar.get(Calendar.MONTH), currCalendar.get(Calendar.DAY_OF_MONTH));

		Date beginDate = aux.getTime();
		aux.add(Calendar.DAY_OF_MONTH, 1);
		Date endDate = aux.getTime();

		Object[][] o = Rel.restoreNAttrs(NewsletterImpl.class, "MAX(:fileSequence)", ":component.pid = ? and :creationDate between ? and ?", false, new Object[] { new pid(componentPid), beginDate, endDate });
		int sequence = 0;
		if (o[0][0] != null)
			sequence = ((Integer) o[0][0]).intValue();

		return ++sequence;
	}

	/**
	 * @param newsletterPid
	 * @param newSequence
	 * @throws BusinessException
	 */
	public void updateSequenceNewsletter(long newsletterPid, Integer newSequence) throws BusinessException {

		Catalog.begin();

		NewsletterImpl newsletterImpl = getNewsletterImpl(newsletterPid);	
		
		if (newsletterImpl != null) {
			newsletterImpl.setSequence(newSequence);
			Catalog.commit();
		}
	}
	
	/**
	 * Remove qualquer tipo de vínculo que exista do componente com algum modelo de algum componente newsletter
	 * @param pid
	 * @throws BusinessException
	 */
	public void removeNewsletterModelsComponents(Long pid) throws BusinessException {

		List<NewsletterModelComponentImpl> parameters = Rel.restoreNWhere(NewsletterModelComponentImpl.class, ":component = ?", pid);
		if (parameters != null && !parameters.isEmpty()) {
    		for(NewsletterModelComponentImpl n: parameters){
    			n.pssDelete();
    			Catalog.commit();
    		}
    	}
	}
	
	/**
	 * @param newsletterModel
	 * @param newsPid
	 * @throws BusinessException
	 */
	public void saveNewsletterModelNews(final Long newsletterModel, final String newsPid) throws BusinessException{
		
		NewsImpl news = Catalog.findByPid(new pid(Long.parseLong(newsPid)), NewsImpl.class);		
		@SuppressWarnings("boxing")
		NewsletterModelComponentImpl newsletterModelComp = Catalog.findByPid(new pid(newsletterModel), NewsletterModelComponentImpl.class);
		
		@SuppressWarnings("unused")
		NewsletterModelNewsImpl modelNewsImpl = new NewsletterModelNewsImpl(newsletterModelComp, news);
		
		Catalog.commit();
	}
	
	/*private void removeNewsletterModelNews(NewsletterModelComponentImpl nmc){
		PList<NewsletterModelNewsImpl> modelNewsImpls = Rel.restoreNWhere(NewsletterModelNewsImpl.class, ":newsletterModelComp = " + nmc.getPid().getSerial());
		for (NewsletterModelNewsImpl newsletterModelNewsImpl : modelNewsImpls){
			NewsletterModelNewsImpl modelNewsImpl = Catalog.findByPid(newsletterModelNewsImpl.getPid(), NewsletterModelNewsImpl.class);
			modelNewsImpl.pssDelete();
			Catalog.commit();
		}
	}*/

}